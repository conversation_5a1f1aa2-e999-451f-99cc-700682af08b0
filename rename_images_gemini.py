#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Skript pro přejmenování obrázků na základě textu přečteného pomocí Google Gemini API
"""

import os
import re
import time
from pathlib import Path
import google.generativeai as genai
from PIL import Image

# Konfigurace
API_KEY = "YOUR_GEMINI_API_KEY"  # Nahraďte svým API klíčem
MAX_FILENAME_LENGTH = 100
SUPPORTED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
DELAY_BETWEEN_REQUESTS = 1  # sekund mezi požadavky na API

def setup_gemini():
    """Nastavení Gemini API"""
    genai.configure(api_key=API_KEY)
    model = genai.GenerativeModel('gemini-1.5-flash')
    return model

def clean_filename(text):
    """
    Vyčistí text pro použití jako název souboru
    """
    if not text:
        return "nerozpoznano"
    
    # Odstraní nové řádky a nahradí je mezerami
    text = text.replace('\n', ' ').replace('\r', ' ')
    
    # Odstraní více mezer za sebou
    text = re.sub(r'\s+', ' ', text)
    
    # Odstraní nepovolené znaky pro názvy souborů
    text = re.sub(r'[<>:"/\\|?*]', '', text)
    
    # Odstraní speciální znaky kromě pomlček, podtržítek a teček
    text = re.sub(r'[^\w\s\-_.]', '', text)
    
    # Nahradí mezery podtržítky
    text = text.replace(' ', '_')
    
    # Ořízne na maximální délku
    if len(text) > MAX_FILENAME_LENGTH:
        text = text[:MAX_FILENAME_LENGTH]
    
    # Odstraní tečky na konci (problematické pro Windows)
    text = text.rstrip('.')
    
    return text.strip() or "nerozpoznano"

def extract_text_from_image(model, image_path):
    """
    Extrahuje text z obrázku pomocí Gemini API
    """
    try:
        # Načtení obrázku
        image = Image.open(image_path)
        
        # Prompt pro OCR
        prompt = """
        Přečti veškerý text v tomto obrázku. 
        Vrať pouze čistý text bez dalších komentářů nebo popisů.
        Pokud je text v češtině, zachovej diakritiku.
        Pokud nevidíš žádný text, vrať "bez_textu".
        """
        
        # Požadavek na API
        response = model.generate_content([prompt, image])
        
        if response.text:
            return response.text.strip()
        else:
            return "bez_textu"
            
    except Exception as e:
        print(f"Chyba při čtení obrázku {image_path}: {e}")
        return "chyba_ocr"

def get_unique_filename(directory, base_name, extension):
    """
    Vytvoří unikátní název souboru přidáním čísla, pokud soubor již existuje
    """
    counter = 1
    original_name = base_name
    
    while True:
        full_path = directory / f"{base_name}{extension}"
        if not full_path.exists():
            return f"{base_name}{extension}"
        
        base_name = f"{original_name}_{counter}"
        counter += 1

def process_images(directory_path="."):
    """
    Zpracuje všechny obrázky ve složce
    """
    directory = Path(directory_path)
    
    # Kontrola API klíče
    if API_KEY == "YOUR_GEMINI_API_KEY":
        print("CHYBA: Nastavte prosím svůj Gemini API klíč v proměnné API_KEY")
        return
    
    # Nastavení Gemini
    try:
        model = setup_gemini()
        print("Gemini API úspěšně inicializováno")
    except Exception as e:
        print(f"Chyba při inicializaci Gemini API: {e}")
        return
    
    # Najde všechny obrázky
    image_files = []
    for ext in SUPPORTED_EXTENSIONS:
        image_files.extend(directory.glob(f"*{ext}"))
        image_files.extend(directory.glob(f"*{ext.upper()}"))
    
    if not image_files:
        print("Nenalezeny žádné obrázky v aktuální složce")
        return
    
    print(f"Nalezeno {len(image_files)} obrázků")
    
    # Zpracování každého obrázku
    for i, image_path in enumerate(image_files, 1):
        print(f"\nZpracovávám {i}/{len(image_files)}: {image_path.name}")
        
        # Extrakce textu
        extracted_text = extract_text_from_image(model, image_path)
        print(f"Přečtený text: {extracted_text[:100]}...")
        
        # Vyčištění textu pro název souboru
        clean_text = clean_filename(extracted_text)
        print(f"Vyčištěný název: {clean_text}")
        
        # Vytvoření nového názvu souboru
        extension = image_path.suffix.lower()
        new_filename = get_unique_filename(directory, clean_text, extension)
        new_path = directory / new_filename
        
        # Přejmenování souboru
        try:
            image_path.rename(new_path)
            print(f"✓ Přejmenováno na: {new_filename}")
        except Exception as e:
            print(f"✗ Chyba při přejmenování: {e}")
        
        # Pauza mezi požadavky
        if i < len(image_files):
            time.sleep(DELAY_BETWEEN_REQUESTS)
    
    print(f"\nHotovo! Zpracováno {len(image_files)} obrázků")

def main():
    """Hlavní funkce"""
    print("=== Přejmenování obrázků pomocí Gemini OCR ===")
    print("Tento skript přečte text z obrázků a přejmenuje je podle obsahu")
    print()
    
    # Kontrola aktuální složky
    current_dir = Path(".")
    print(f"Pracovní složka: {current_dir.absolute()}")
    
    # Spuštění zpracování
    process_images()

if __name__ == "__main__":
    main()
