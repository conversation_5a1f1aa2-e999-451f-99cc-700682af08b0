#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Skript pro přejmenování obrázků na základě textu přečteného pomocí Google Gemini API
"""

import os
import re
import time
import json
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
import google.generativeai as genai
from PIL import Image

# Bezpečné načtení API klíče
def load_api_key():
    """Bezpečně načte API klíč z konfiguračního souboru"""
    try:
        # Pokus o načtení z config.py
        from config import GEMINI_API_KEY
        return GEMINI_API_KEY
    except ImportError:
        # Pokus o načtení z environment proměnné
        api_key = os.getenv('GEMINI_API_KEY')
        if api_key:
            return api_key

        # Pokud nic nenalezeno, zobraz chybu
        print("CHYBA: API klíč nenalezen!")
        print("Možnosti:")
        print("1. Vytvořte soubor 'config.py' s proměnnou GEMINI_API_KEY")
        print("2. Nastavte environment proměnnou GEMINI_API_KEY")
        return None

# Konfigurace
SUPPORTED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']

# Načtení konfigurace
try:
    from config import MAX_FILENAME_LENGTH, DELAY_BETWEEN_REQUESTS
except ImportError:
    # Výchozí hodnoty, pokud config.py neexistuje
    MAX_FILENAME_LENGTH = 100
    DELAY_BETWEEN_REQUESTS = 6

# Limity pro Gemini 2.5 Flash Free Tier
RPM_LIMIT = 10      # Požadavků za minutu
TPM_LIMIT = 250000  # Tokenů za minutu
RPD_LIMIT = 250     # Požadavků za den

# Soubor pro sledování využití API
USAGE_LOG_FILE = "gemini_usage_log.json"

class APILimitManager:
    """Správce limitů pro Gemini API"""

    def __init__(self):
        self.usage_log_file = USAGE_LOG_FILE
        self.load_usage_log()

    def load_usage_log(self):
        """Načte log využití API"""
        try:
            if os.path.exists(self.usage_log_file):
                with open(self.usage_log_file, 'r', encoding='utf-8') as f:
                    self.usage_log = json.load(f)
            else:
                self.usage_log = {
                    "daily_requests": {},
                    "minute_requests": [],
                    "daily_tokens": {},
                    "minute_tokens": []
                }
        except Exception:
            self.usage_log = {
                "daily_requests": {},
                "minute_requests": [],
                "daily_tokens": {},
                "minute_tokens": []
            }

    def save_usage_log(self):
        """Uloží log využití API"""
        try:
            with open(self.usage_log_file, 'w', encoding='utf-8') as f:
                json.dump(self.usage_log, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Varování: Nelze uložit log využití: {e}")

    def clean_old_records(self):
        """Vyčistí staré záznamy"""
        now = datetime.now()
        today = now.strftime("%Y-%m-%d")
        one_minute_ago = now - timedelta(minutes=1)

        # Vyčistí minutové záznamy starší než 1 minutu
        self.usage_log["minute_requests"] = [
            req for req in self.usage_log["minute_requests"]
            if datetime.fromisoformat(req) > one_minute_ago
        ]

        self.usage_log["minute_tokens"] = [
            token for token in self.usage_log["minute_tokens"]
            if datetime.fromisoformat(token["timestamp"]) > one_minute_ago
        ]

        # Ponechá pouze dnešní denní záznamy
        if today not in self.usage_log["daily_requests"]:
            self.usage_log["daily_requests"] = {today: 0}
        else:
            self.usage_log["daily_requests"] = {today: self.usage_log["daily_requests"].get(today, 0)}

        if today not in self.usage_log["daily_tokens"]:
            self.usage_log["daily_tokens"] = {today: 0}
        else:
            self.usage_log["daily_tokens"] = {today: self.usage_log["daily_tokens"].get(today, 0)}

    def can_make_request(self):
        """Zkontroluje, zda lze provést požadavek"""
        self.clean_old_records()

        now = datetime.now()
        today = now.strftime("%Y-%m-%d")

        # Kontrola denních limitů
        daily_requests = self.usage_log["daily_requests"].get(today, 0)
        if daily_requests >= RPD_LIMIT:
            return False, f"Dosažen denní limit požadavků ({RPD_LIMIT})"

        # Kontrola minutových limitů
        minute_requests = len(self.usage_log["minute_requests"])
        if minute_requests >= RPM_LIMIT:
            return False, f"Dosažen minutový limit požadavků ({RPM_LIMIT})"

        return True, "OK"

    def record_request(self, estimated_tokens=1000):
        """Zaznamená provedený požadavek"""
        now = datetime.now()
        today = now.strftime("%Y-%m-%d")
        timestamp = now.isoformat()

        # Zaznamenání požadavku
        self.usage_log["minute_requests"].append(timestamp)
        self.usage_log["daily_requests"][today] = self.usage_log["daily_requests"].get(today, 0) + 1

        # Zaznamenání tokenů (odhad)
        self.usage_log["minute_tokens"].append({
            "timestamp": timestamp,
            "tokens": estimated_tokens
        })
        self.usage_log["daily_tokens"][today] = self.usage_log["daily_tokens"].get(today, 0) + estimated_tokens

        self.save_usage_log()

    def get_usage_stats(self):
        """Vrátí statistiky využití"""
        self.clean_old_records()
        today = datetime.now().strftime("%Y-%m-%d")

        daily_requests = self.usage_log["daily_requests"].get(today, 0)
        minute_requests = len(self.usage_log["minute_requests"])
        daily_tokens = self.usage_log["daily_tokens"].get(today, 0)
        minute_tokens = sum(token["tokens"] for token in self.usage_log["minute_tokens"])

        return {
            "daily_requests": daily_requests,
            "daily_requests_limit": RPD_LIMIT,
            "minute_requests": minute_requests,
            "minute_requests_limit": RPM_LIMIT,
            "daily_tokens": daily_tokens,
            "daily_tokens_limit": TPM_LIMIT,
            "minute_tokens": minute_tokens,
            "minute_tokens_limit": TPM_LIMIT
        }

def setup_gemini():
    """Nastavení Gemini API"""
    api_key = load_api_key()
    if not api_key:
        raise ValueError("API klíč nenalezen")

    genai.configure(api_key=api_key)
    model = genai.GenerativeModel('gemini-2.0-flash-exp')  # Použití Gemini 2.5 Flash
    return model

def clean_filename(text):
    """
    Vyčistí text pro použití jako název souboru
    Formát: Velké první písmeno, mezery místo podtržítek
    """
    if not text:
        return "Nerozpoznano"

    # Odstraní nové řádky a nahradí je mezerami
    text = text.replace('\n', ' ').replace('\r', ' ')

    # Nahradí podtržítka mezerami
    text = text.replace('_', ' ')

    # Odstraní více mezer za sebou
    text = re.sub(r'\s+', ' ', text)

    # Odstraní nepovolené znaky pro názvy souborů (kromě mezer)
    text = re.sub(r'[<>:"/\\|?*]', '', text)

    # Odstraní speciální znaky kromě písmen, číslic, mezer, pomlček a teček
    text = re.sub(r'[^\w\s\-.]', '', text)

    # Převede na malá písmena a pak udělá velké první písmeno
    text = text.lower().strip()
    if text:
        text = text[0].upper() + text[1:]

    # Ořízne na maximální délku
    if len(text) > MAX_FILENAME_LENGTH:
        text = text[:MAX_FILENAME_LENGTH].strip()

    # Odstraní tečky na konci (problematické pro Windows)
    text = text.rstrip('.')

    return text.strip() or "Nerozpoznano"

def extract_text_from_image(model, image_path, limit_manager):
    """
    Extrahuje text z obrázku pomocí Gemini API s respektováním limitů
    """
    try:
        # Kontrola limitů před požadavkem
        can_request, message = limit_manager.can_make_request()
        if not can_request:
            print(f"Nelze provést požadavek: {message}")
            return "limit_prekrocen"

        # Načtení obrázku
        image = Image.open(image_path)

        # Prompt pro OCR
        prompt = """
        Přečti veškerý text v tomto obrázku.
        Vrať pouze čistý text bez dalších komentářů nebo popisů.
        Pokud je text v češtině, zachovej diakritiku.
        Pokud nevidíš žádný text, vrať "bez_textu".
        """

        # Odhad tokenů (obrázek + prompt)
        estimated_tokens = 1500  # Konzervativní odhad pro obrázek + prompt

        # Zaznamenání požadavku před jeho provedením
        limit_manager.record_request(estimated_tokens)

        # Požadavek na API
        response = model.generate_content([prompt, image])

        if response.text:
            return response.text.strip()
        else:
            return "bez_textu"

    except Exception as e:
        print(f"Chyba při čtení obrázku {image_path}: {e}")
        return "chyba_ocr"

def is_processed_filename(filename):
    """
    Určí, zda název souboru vypadá jako už zpracovaný (obsahuje smysluplný text)
    """
    # Odstraní příponu
    name_without_ext = Path(filename).stem

    # Seznam vzorů pro nezpracované soubory (náhodné názvy)
    random_patterns = [
        r'^[a-z]{2,6}$',           # dfds, ssdd, dsfdf
        r'^[a-z]+\d+$',            # da_1, test1, test2
        r'^[a-z]+_\d+$',           # da_1
        r'^[a-z]{1,3}\d{1,3}$',    # abc123
        r'^\d+[a-z]+$',            # 123abc
        r'^[a-z]+[a-z]+[a-z]+$'    # dfdsf (opakující se písmena)
    ]

    # Kontrola, zda název odpovídá vzoru náhodného názvu
    for pattern in random_patterns:
        if re.match(pattern, name_without_ext.lower()):
            return False

    # Pokud obsahuje mezery, je to zpracovaný soubor
    if ' ' in name_without_ext:
        return True

    # Pokud začína velkým písmenem a obsahuje smysluplný text (delší než 5 znaků)
    if name_without_ext[0].isupper() and len(name_without_ext) > 5:
        return True

    # Pokud obsahuje podtržítka nebo pomlčky a je delší než 4 znaky
    if ('_' in name_without_ext or '-' in name_without_ext) and len(name_without_ext) > 4:
        return True

    return False

def get_unique_filename(directory, base_name, extension):
    """
    Vytvoří unikátní název souboru přidáním čísla, pokud soubor již existuje
    """
    counter = 1
    original_name = base_name

    while True:
        full_path = directory / f"{base_name}{extension}"
        if not full_path.exists():
            return f"{base_name}{extension}"

        base_name = f"{original_name} {counter}"  # Mezera místo podtržítka
        counter += 1

def process_images(directory_path="."):
    """
    Zpracuje všechny obrázky ve složce s respektováním API limitů
    """
    directory = Path(directory_path)

    # Kontrola API klíče
    api_key = load_api_key()
    if not api_key:
        print("CHYBA: API klíč nenalezen! Zkontrolujte config.py nebo environment proměnnou.")
        return

    # Inicializace správce limitů
    limit_manager = APILimitManager()

    # Zobrazení aktuálního využití
    stats = limit_manager.get_usage_stats()
    print("=== Aktuální využití API ===")
    print(f"Denní požadavky: {stats['daily_requests']}/{stats['daily_requests_limit']}")
    print(f"Minutové požadavky: {stats['minute_requests']}/{stats['minute_requests_limit']}")
    print(f"Denní tokeny: {stats['daily_tokens']}/{stats['daily_tokens_limit']}")
    print(f"Minutové tokeny: {stats['minute_tokens']}/{stats['minute_tokens_limit']}")
    print()

    # Nastavení Gemini
    try:
        model = setup_gemini()
        print("Gemini API úspěšně inicializováno")
    except Exception as e:
        print(f"Chyba při inicializaci Gemini API: {e}")
        return

    # Najde všechny obrázky (pouze ty, které skutečně existují)
    image_files = []
    for ext in SUPPORTED_EXTENSIONS:
        image_files.extend([f for f in directory.glob(f"*{ext}") if f.exists()])
        image_files.extend([f for f in directory.glob(f"*{ext.upper()}") if f.exists()])

    # Odfiltruje soubory, které vypadají jako už zpracované
    original_count = len(image_files)
    image_files = [f for f in image_files if not is_processed_filename(f.name)]
    filtered_count = original_count - len(image_files)

    if not image_files:
        if original_count > 0:
            print(f"Nalezeno {original_count} obrázků, ale všechny vypadají jako už zpracované")
            print("Tip: Soubory s popisnými názvy (ne náhodné znaky) jsou považovány za zpracované")
        else:
            print("Nenalezeny žádné obrázky v aktuální složce")
        return

    print(f"Nalezeno {len(image_files)} obrázků k zpracování")
    if filtered_count > 0:
        print(f"Přeskočeno {filtered_count} souborů (vypadají jako už zpracované)")

    # Kontrola, zda lze zpracovat všechny obrázky
    remaining_daily = stats['daily_requests_limit'] - stats['daily_requests']
    if len(image_files) > remaining_daily:
        print(f"VAROVÁNÍ: Máte {len(image_files)} obrázků, ale zbývá pouze {remaining_daily} denních požadavků.")
        print(f"Bude zpracováno pouze prvních {remaining_daily} obrázků.")
        image_files = image_files[:remaining_daily]

    # Zpracování každého obrázku
    processed = 0
    skipped = 0

    for i, image_path in enumerate(image_files, 1):
        print(f"\nZpracovávám {i}/{len(image_files)}: {image_path.name}")

        # Kontrola, zda soubor stále existuje (mohl být už přejmenován)
        if not image_path.exists():
            print(f"⚠️ Soubor {image_path.name} už neexistuje (pravděpodobně už byl přejmenován)")
            skipped += 1
            continue

        # Kontrola limitů před každým požadavkem
        can_request, message = limit_manager.can_make_request()
        if not can_request:
            print(f"Zastavuji zpracování: {message}")
            break

        # Extrakce textu
        try:
            extracted_text = extract_text_from_image(model, image_path, limit_manager)

            if extracted_text == "limit_prekrocen":
                print("Dosažen limit API, zastavuji zpracování")
                break

            print(f"Přečtený text: {extracted_text[:100]}...")

            # Vyčištění textu pro název souboru
            clean_text = clean_filename(extracted_text)
            print(f"Vyčištěný název: {clean_text}")

            # Vytvoření nového názvu souboru
            extension = image_path.suffix.lower()
            new_filename = get_unique_filename(directory, clean_text, extension)
            new_path = directory / new_filename

            # Přejmenování souboru
            try:
                image_path.rename(new_path)
                print(f"✓ Přejmenováno na: {new_filename}")
                processed += 1
            except Exception as e:
                print(f"✗ Chyba při přejmenování: {e}")

        except Exception as e:
            print(f"✗ Chyba při zpracování {image_path.name}: {e}")
            skipped += 1
            continue

        # Pauza mezi požadavky (respektování RPM limitu)
        remaining_files = len([f for f in image_files[i:] if f.exists()])
        if remaining_files > 0:
            print(f"Čekám {DELAY_BETWEEN_REQUESTS} sekund před dalším požadavkem...")
            time.sleep(DELAY_BETWEEN_REQUESTS)

    # Finální statistiky
    final_stats = limit_manager.get_usage_stats()
    print(f"\n=== Hotovo! ===")
    print(f"Úspěšně zpracováno: {processed} obrázků")
    print(f"Přeskočeno: {skipped} souborů (už neexistují nebo chyba)")
    print(f"Celkem nalezeno: {len(image_files)} souborů")
    print(f"Využití dnes: {final_stats['daily_requests']}/{final_stats['daily_requests_limit']} požadavků")
    print(f"Zbývá dnes: {final_stats['daily_requests_limit'] - final_stats['daily_requests']} požadavků")

def main():
    """Hlavní funkce"""
    print("=== Přejmenování obrázků pomocí Gemini OCR ===")
    print("Tento skript přečte text z obrázků a přejmenuje je podle obsahu")
    print()
    
    # Kontrola aktuální složky
    current_dir = Path(".")
    print(f"Pracovní složka: {current_dir.absolute()}")
    
    # Spuštění zpracování
    process_images()

if __name__ == "__main__":
    main()
