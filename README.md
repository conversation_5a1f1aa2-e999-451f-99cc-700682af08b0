# Přejmenování obráz<PERSON><PERSON> pomocí Google Gemini OCR

Tento Python skript automaticky přečte text z obrázků pomocí Google Gemini API a přejmenuje soubory podle přečteného obsahu.

## Požadavky

1. **Python 3.7+**
2. **Google Gemini API klíč** - získejte na [Google AI Studio](https://makersuite.google.com/app/apikey)
3. **Potřebné knihovny** (viz requirements.txt)

## Instalace

1. Nainstalujte potřebné knihovny:
```bash
pip install -r requirements.txt
```

2. Získejte API klíč pro Google Gemini:
   - Jděte na https://makersuite.google.com/app/apikey
   - Vytvořte nový API klíč
   - Zkopírujte ho

3. Upravte soubor `rename_images_gemini.py`:
   - Najděte řádek `API_KEY = "YOUR_GEMINI_API_KEY"`
   - Nahraďte `YOUR_GEMINI_API_KEY` svým skutečným API klíčem

## Použití

1. Umístěte skript do složky s obrázky
2. Spusťte skript:
```bash
python rename_images_gemini.py
```

## Podporované formáty

- JPG/JPEG
- PNG
- BMP
- TIFF

## Funkce

- **OCR pomocí Gemini**: Používá nejnovější Google Gemini model pro přečtení textu
- **Čištění názvů**: Automaticky odstraní nepovolené znaky a upraví text pro použití jako název souboru
- **Unikátní názvy**: Pokud soubor se stejným názvem již existuje, přidá číslo
- **Zachování diakritiky**: Podporuje českou diakritiku
- **Bezpečnost**: Kontroluje délku názvů a nepovolené znaky
- **Pauzy mezi požadavky**: Respektuje limity API

## Nastavení

V souboru `rename_images_gemini.py` můžete upravit:

- `MAX_FILENAME_LENGTH`: Maximální délka názvu souboru (výchozí: 100)
- `DELAY_BETWEEN_REQUESTS`: Pauza mezi požadavky na API (výchozí: 1 sekunda)
- `SUPPORTED_EXTENSIONS`: Podporované formáty obrázků

## Poznámky

- Skript vytvoří zálohu původních názvů v konzoli
- Pokud OCR selže, soubor se přejmenuje na "chyba_ocr"
- Pokud není nalezen žádný text, použije se "bez_textu"
- API klíč je potřeba pro každé spuštění

## Řešení problémů

**Chyba API klíče**: Zkontrolujte, že jste správně nastavili API_KEY v kódu

**Chyba importu**: Nainstalujte požadavky pomocí `pip install -r requirements.txt`

**Pomalé zpracování**: Gemini API má limity, skript automaticky čeká mezi požadavky
