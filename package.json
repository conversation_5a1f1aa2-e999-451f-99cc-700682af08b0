{"name": "ocr-image-renamer", "version": "1.0.0", "description": "Aplikace pro přejmenování obrázků pomocí Google Gemini OCR", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "dist": "electron-builder --publish=never"}, "keywords": ["ocr", "gemini", "image", "rename", "electron"], "author": "OCR Renamer", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1"}, "dependencies": {"axios": "^1.6.0"}, "build": {"appId": "com.ocrrenamer.app", "productName": "OCR Image Renamer", "directories": {"output": "dist"}, "files": ["main.js", "renderer.js", "index.html", "styles.css", "assets/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}