🚀 OCR IMAGE RENAMER - RYCHLÝ START
=====================================

📖 KOMPLETNÍ NÁVOD: Otevřete soubor "Návod.md"

⚡ RYCHLÝ START (5 kroků):

1️⃣ NAINSTALUJTE NODE.JS
   → Jděte na: https://nodejs.org/
   → Stáhněte LTS verzi (levé tla<PERSON>)
   → Nainstalujte (Next, Next, Install, Finish)

2️⃣ ZÍSKEJTE API KLÍČ
   → Jděte na: https://makersuite.google.com/app/apikey
   → Přihlaste se Google účtem
   → Klikněte "Create API Key"
   → Zkopírujte klíč (vypadá jako: AIzaSyDs8x56ahAxNlrHGcf4L6qpoNYOnALWDgM)

3️⃣ SPUSŤTE APLIKACI
   → Dvojklik na "start_electron.bat"
   → Počkejte (při prvním spuštění 2-5 minut)

4️⃣ NASTAVTE API KLÍČ
   → V aplikaci klikněte "⚙️ Nastavení"
   → Vložte API klíč
   → Klikněte "Uložit"

5️⃣ PŘEJMENUJTE OBRÁZKY
   → Klikněte "📁 Vybrat složku s obrázky"
   → Vyberte složku
   → Klikněte "🚀 Zpracovat všechny"

🎉 HOTOVO! Vaše obrázky mají nové názvy podle obsahu!

📁 SOUBORY V TÉTO SLOŽCE:
========================

🐍 PYTHON VERZE (pro pokročilé):
   • rename_images_gemini.py - hlavní skript
   • config.py - nastavení s API klíčem
   • requirements.txt - závislosti
   • README.md - dokumentace

💻 ELECTRON VERZE (pro všechny):
   • start_electron.bat - spouštěč (DVOJKLIK!)
   • package.json - konfigurace
   • main.js - backend
   • renderer.js - frontend
   • index.html - rozhraní
   • styles.css - design
   • README_Electron.md - technická dokumentace

📖 NÁVODY:
   • Návod.md - KOMPLETNÍ NÁVOD PRO ZAČÁTEČNÍKY
   • ZAČÍNÁME.txt - tento soubor

⚠️ DŮLEŽITÉ:
============
• Potřebujete internetové připojení
• API klíč je ZDARMA (250 obrázků denně)
• Zálohujte si obrázky před zpracováním
• Aplikace funguje s JPG, PNG, BMP, TIFF

🆘 PROBLÉMY?
============
1. Přečtěte si "Návod.md" - řeší 99% problémů
2. Restartujte počítač
3. Přeinstalujte Node.js
4. Zkuste Python verzi

📞 RYCHLÁ POMOC:
===============
• Aplikace se nespustí → Nainstalujte Node.js
• "API klíč nenalezen" → Vložte klíč v nastavení
• Pomalé zpracování → Zkontrolujte internet
• Špatné přečtení → Použijte kvalitnější obrázky

🎯 CO APLIKACE DĚLÁ:
===================
Místo názvů jako:
❌ IMG_001.jpg, DSC_002.jpg, Photo123.jpg

Budete mít:
✅ Hostýn.jpg, Příbram.jpg, Karlštejn.jpg

Aplikace přečte text z obrázku a podle něj soubor přejmenuje!

🚀 UŽIJTE SI TO!
