# 📖 Kompletní návod pro začátečníky
## OCR Image Renamer - Automatické přejmenování obrázků

Tento návod vás krok za krokem provede instalací a použitím aplikace pro automatické přejmenování obrázků pomocí umělé inteligence.

---

## 🎯 Co tato aplikace dělá?

**Jednoduše řečeno:** Podíváte se na obrázek s textem (např. pohlednici, dokument, ceduli) a aplikace:
1. **Přečte text** z obrázku pomocí Google AI
2. **Přejmenuje soubor** podle přečteného textu
3. **Místo** `IMG_001.jpg` budete mít `Hostýn.jpg` nebo `Příbram.jpg`

---

## 📋 Co budete potřebovat

### 1. <PERSON><PERSON><PERSON><PERSON><PERSON> s Windows
- Jakýkoliv počí<PERSON>č s Windows 10 nebo novější
- Připojení k internetu (pro stažení a API)

### 2. Google účet
- Potřebujete Gmail nebo jiný Google účet (zdarma)
- Použije se pro získání API klíče

---

## 🚀 KROK 1: Stažení a instalace Node.js

### Co je Node.js?
Node.js je program, který umožňuje spouštět naši aplikaci. Je to jako "motor" pro aplikaci.

### Jak nainstalovat:

1. **Otevřete webový prohlížeč** (Chrome, Firefox, Edge...)

2. **Jděte na stránku:** https://nodejs.org/

3. **Uvidíte zelenou stránku** s dvěma tlačítky ke stažení:
   ```
   [20.x.x LTS]    [21.x.x Current]
   ```

4. **Klikněte na LEVÉ tlačítko** (LTS) - to je stabilní verze

5. **Stáhne se soubor** (např. `node-v20.10.0-x64.msi`)

6. **Najděte stažený soubor** (obvykle ve složce Stažené soubory)

7. **Dvojklik na soubor** a spustí se instalace

8. **Postupujte podle průvodce:**
   - Klikněte "Next" (Další)
   - Zaškrtněte "I accept..." (Souhlasím)
   - Klikněte "Next" několikrát
   - Klikněte "Install" (Instalovat)
   - Počkejte na dokončení
   - Klikněte "Finish" (Dokončit)

### Ověření instalace:

1. **Stiskněte** `Windows + R` (otevře se okno "Spustit")

2. **Napište:** `cmd` a stiskněte Enter

3. **Otevře se černé okno** (příkazový řádek)

4. **Napište:** `node --version` a stiskněte Enter

5. **Měli byste vidět:** `v20.10.0` (nebo podobné číslo)

6. **Napište:** `npm --version` a stiskněte Enter

7. **Měli byste vidět:** `10.2.3` (nebo podobné číslo)

8. **Zavřete okno** napsáním `exit` a Enter

✅ **Pokud vidíte čísla verzí, Node.js je správně nainstalován!**

---

## 🔑 KROK 2: Získání Google Gemini API klíče

### Co je API klíč?
API klíč je jako "heslo", které umožňuje naší aplikaci komunikovat s Google AI. Je zdarma pro běžné použití.

### Jak získat API klíč:

1. **Otevřete prohlížeč** a jděte na: https://makersuite.google.com/app/apikey

2. **Přihlaste se** svým Google účtem (Gmail)

3. **Uvidíte stránku** "API Keys" (API klíče)

4. **Klikněte na modré tlačítko** "Create API Key" (Vytvořit API klíč)

5. **Vyberte projekt** nebo vytvořte nový:
   - Pokud máte projekt, vyberte ho
   - Pokud ne, klikněte "Create API key in new project"

6. **Počkejte chvilku** - Google vytvoří váš klíč

7. **Zobrazí se okno s klíčem** - vypadá takto:
   ```
   AIzaSyDs8x56ahAxNlrHGcf4L6qpoNYOnALWDgM
   ```

8. **DŮLEŽITÉ:** Klikněte na ikonu "kopírovat" 📋 vedle klíče

9. **Vložte klíč do poznámkového bloku** a uložte ho - budete ho potřebovat!

⚠️ **POZOR:** Nikomu nesdílejte svůj API klíč! Je to vaše osobní heslo.

---

## 💻 KROK 3: Spuštění aplikace

### Automatický způsob (doporučený):

1. **Najděte soubor** `start_electron.bat` ve složce s aplikací

2. **Dvojklik na soubor** `start_electron.bat`

3. **Otevře se černé okno** které:
   - Zkontroluje Node.js
   - Nainstaluje potřebné součásti (při prvním spuštění)
   - Spustí aplikaci

4. **Počkejte** - při prvním spuštění to může trvat 2-5 minut

5. **Aplikace se otevře** v novém okně

### Manuální způsob:

1. **Otevřete složku** s aplikací

2. **Držte Shift a klikněte pravým tlačítkem** do prázdného místa

3. **Vyberte** "Otevřít okno PowerShell zde" nebo "Otevřít příkazový řádek zde"

4. **Napište:** `npm install` a stiskněte Enter (pouze při prvním spuštění)

5. **Počkejte na dokončení** (2-5 minut)

6. **Napište:** `npm start` a stiskněte Enter

7. **Aplikace se spustí**

---

## ⚙️ KROK 4: Nastavení aplikace

### První spuštění:

1. **Aplikace se otevře** - uvidíte krásné modré rozhraní

2. **Klikněte na tlačítko** "⚙️ Nastavení" vpravo nahoře

3. **Otevře se okno nastavení**

4. **Do pole "Google Gemini API klíč"** vložte váš klíč z kroku 2:
   - Klikněte do pole
   - Stiskněte `Ctrl + V` (vložit)

5. **Zkontrolujte další nastavení:**
   - **Maximální délka názvu:** 100 (můžete nechat)
   - **Pauza mezi požadavky:** 6 sekund (můžete nechat)

6. **Klikněte** "Uložit"

7. **Nastavení se zavře** - aplikace je připravena!

---

## 🖼️ KROK 5: Použití aplikace

### Přejmenování obrázků:

1. **Připravte si složku s obrázky** (JPG, PNG, BMP...)

2. **V aplikaci klikněte** "📁 Vybrat složku s obrázky"

3. **Vyberte složku** s vašimi obrázky

4. **Aplikace zobrazí** všechny nalezené obrázky s náhledy

5. **Uvidíte statistiky:**
   ```
   12 celkem | 8 k zpracování
   ```

6. **Vyberte způsob zpracování:**

   **a) Zpracovat všechny:**
   - Klikněte "🚀 Zpracovat všechny"
   - Zpracuje všechny nezpracované obrázky

   **b) Zpracovat vybrané:**
   - Klikněte "✓ Vybrat" u obrázků, které chcete zpracovat
   - Klikněte "✨ Zpracovat vybrané"

   **c) Zpracovat jednotlivě:**
   - Klikněte "🚀 Zpracovat" u konkrétního obrázku

### Sledování postupu:

1. **Zobrazí se progress bar** (ukazatel postupu)

2. **Uvidíte live log:**
   ```
   [14:30:15] Zpracovávám: IMG_001.jpg
   [14:30:18] Přečtený text: Hostýn...
   [14:30:18] Nový název: Hostýn
   [14:30:18] ✓ Přejmenováno na: Hostýn.jpg
   [14:30:18] Čekám 6 sekund...
   ```

3. **Můžete zrušit** kliknutím na "Zrušit"

### Výsledek:

- **Původní:** `IMG_001.jpg`, `DSC_002.jpg`, `Photo123.jpg`
- **Nové:** `Hostýn.jpg`, `Příbram.jpg`, `Karlštejn.jpg`

---

## 🔧 Řešení problémů

### Aplikace se nespustí:

**Problém:** Dvojklik na `start_electron.bat` nic nedělá
**Řešení:**
1. Zkontrolujte, že máte nainstalován Node.js (krok 1)
2. Klikněte pravým tlačítkem na `start_electron.bat`
3. Vyberte "Spustit jako správce"

**Problém:** Chyba "npm: command not found"
**Řešení:**
1. Restartujte počítač
2. Přeinstalujte Node.js (krok 1)

### Chyba API klíče:

**Problém:** "API klíč nenalezen" nebo "Unauthorized"
**Řešení:**
1. Zkontrolujte, že jste správně zkopírovali klíč
2. Jděte na https://makersuite.google.com/app/apikey
3. Ověřte, že klíč existuje a je aktivní
4. Zkopírujte ho znovu do nastavení

### Pomalé zpracování:

**Problém:** Aplikace zpracovává velmi pomalu
**Řešení:**
1. Zkontrolujte internetové připojení
2. V nastavení zvyšte "Pauza mezi požadavky" na 10-15 sekund
3. Zpracovávejte méně obrázků najednou

### Špatné přečtení textu:

**Problém:** AI špatně přečetla text z obrázku
**Řešení:**
1. Použijte obrázky s čitelným textem
2. Vyhněte se rozmazaným nebo malým textům
3. Zkuste obrázky s kontrastním textem (černý text na bílém pozadí)

---

## 📊 Limity a omezení

### Google Gemini Free Tier:
- **250 požadavků za den** - stačí na ~250 obrázků denně
- **10 požadavků za minutu** - aplikace automaticky čeká
- **Zdarma** - neplatíte nic

### Podporované formáty:
- ✅ JPG, JPEG
- ✅ PNG
- ✅ BMP
- ✅ TIFF

### Doporučení:
- **Zpracovávejte po menších dávkách** (20-50 obrázků)
- **Používejte kvalitní obrázky** s čitelným textem
- **Zálohujte si původní soubory** před zpracováním

---

## 🎉 Gratulujeme!

Nyní umíte používat OCR Image Renamer! Vaše obrázky budou mít smysluplné názvy místo náhodných kódů.

### Tipy pro pokročilé:
- Můžete vytvořit **zástupce** na ploše pro rychlé spuštění
- **Zkopírujte celou složku** na jiný počítač - bude fungovat
- **Sdílejte s přáteli** - stačí zkopírovat složku

### Potřebujete pomoc?
- Přečtěte si `README_Electron.md` pro technické detaily
- Zkontrolujte log soubory při problémech
- Restartujte aplikaci při neočekávaných chybách

**Užijte si automatické přejmenování obrázků! 🚀**

---

## ❓ Často kladené otázky (FAQ)

### Q: Je aplikace zdarma?
**A:** Ano! Aplikace je zdarma. Google Gemini API má free tier s 250 požadavky denně, což stačí pro běžné použití.

### Q: Potřebuji být online?
**A:** Ano, pro OCR (čtení textu) potřebujete internet. Ostatní funkce fungují offline.

### Q: Mohu aplikaci používat komerčně?
**A:** Ano, aplikace má MIT licenci. Zkontrolujte si ale podmínky Google Gemini API.

### Q: Podporuje aplikace češtinu?
**A:** Ano! Google Gemini umí číst český text včetně diakritiky (háčky, čárky).

### Q: Co když AI špatně přečte text?
**A:** Můžete soubor přejmenovat ručně nebo zkusit zpracovat znovu s lepším obrázkem.

### Q: Mohu zpracovat tisíce obrázků?
**A:** Ano, ale respektujte denní limit 250 požadavků. Pro více obrázků zpracovávejte po částech.

### Q: Funguje na Mac nebo Linux?
**A:** Aplikace je navržena pro Windows. Na Mac/Linux použijte Python verzi.

### Q: Jak vytvořit portable .exe?
**A:** Spusťte `npm run build` - vytvoří se .exe soubor ve složce `dist/`.

---

## 🆘 Nouzová pomoc

### Aplikace úplně nefunguje:
1. **Restartujte počítač**
2. **Přeinstalujte Node.js**
3. **Smažte složku `node_modules`**
4. **Spusťte znovu `start_electron.bat`**

### Ztratil jsem API klíč:
1. **Jděte na:** https://makersuite.google.com/app/apikey
2. **Vytvořte nový klíč**
3. **Vložte do nastavení aplikace**

### Chci vrátit původní názvy:
- **Bohužel to nejde automaticky**
- **Příště si zálohujte složku před zpracováním**
- **Nebo použijte kopii složky pro testování**

---

## 📞 Kontakt a podpora

Pokud máte problémy, které tento návod nevyřešil:

1. **Zkontrolujte log soubory** v aplikaci
2. **Přečtěte si `README_Electron.md`** pro technické detaily
3. **Restartujte aplikaci** a zkuste znovu
4. **Zkuste Python verzi** jako alternativu

**Hodně štěstí s přejmenováváním obrázků! 🎯**
